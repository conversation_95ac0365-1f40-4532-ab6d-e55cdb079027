## ShadowGit 技术方案 

在 Node.js/Core 层实现一个“影子 Git 检查点（Shadow Checkpoints）”服务，并把它无缝接入 Continue。方案避开 Roo Code 的 VS Code 专属实现细节，但复用其核心思路（独立影子仓、自动快照、可回滚）与落地经验（忽略规则、磁盘清理、串行 Git 操作等）。参考依据见文末。 ([Roo Code Docs][1], [GitHub][2], [Continue][3], [Continue][4], [Continue AI][5])

---

# 一、安装依赖（Node.js/Core 层）

```bash
npm i simple-git chokidar ignore fs-extra p-queue
```

* `simple-git`：封装 Git 子进程，稳定易用。
* `chokidar`：跨平台文件监听。
* `ignore`：解析 `.gitignore` 规则并复用到监听/提交。
* `fs-extra`：目录复制、删除等。
* `p-queue`：将 Git 操作**串行**队列化，避免并发损坏仓库（Roo 也强调串行提交与队列化）。 ([Roo Code Docs][1])

> 为避免污染你的主仓库，本方案将**每个会话/任务**使用**独立的影子仓路径**（与 Roo 的“每个任务一个仓库”一致）。同时默认遵循 `.gitignore`，并补充一份常见大目录忽略清单，减少磁盘与提交频率。 ([Roo Code Docs][1], [GitHub][2])

---

# 二、影子检查点服务（可直接复制使用）

把下面这段代码放到你的 Core 层（例如 `src/checkpoints/ShadowCheckpointService.ts`），作为**可独立单元**对外暴露：

```ts
// src/checkpoints/ShadowCheckpointService.ts
import path from "node:path";
import os from "node:os";
import { promises as fs } from "node:fs";
import fse from "fs-extra";
import ignore from "ignore";
import chokidar from "chokidar";
import simpleGit, { SimpleGit } from "simple-git";
import PQueue from "p-queue";

type Options = {
  workspaceDir: string;           // 用户真实工作区
  sessionId: string;              // 会话/任务ID，每个ID一个影子仓
  baseDir?: string;               // 影子仓根目录（默认~/.continue/checkpoints）
  debounceMs?: number;            // 事件合并时间窗
  extraIgnores?: string[];        // 额外忽略模式
  initSnapshot?: boolean;         // 是否在会话开始立即生成初始快照
};

export class ShadowCheckpointService {
  private opts: Required<Options>;
  private shadowRoot: string;
  private shadowRepoDir: string;  // 影子仓工作副本（含 .git）
  private git!: SimpleGit;
  private ig = ignore();
  private q = new PQueue({ concurrency: 1 }); // 串行化
  private watcher?: chokidar.FSWatcher;
  private pending = false;
  private timer?: NodeJS.Timeout;

  constructor(opts: Options) {
    this.opts = {
      baseDir: path.join(os.homedir(), ".continue", "checkpoints"),
      debounceMs: 500,
      extraIgnores: [],
      initSnapshot: true,
      ...opts,
    };
    this.shadowRoot = this.opts.baseDir;
    this.shadowRepoDir = path.join(this.shadowRoot, this.opts.sessionId);
  }

  /** 初始化影子仓（独立于主仓库） */
  async init() {
    await fse.ensureDir(this.shadowRepoDir);

    // 构建忽略规则：项目 .gitignore + 内置大目录
    const giPath = path.join(this.opts.workspaceDir, ".gitignore");
    if (await fse.pathExists(giPath)) {
      this.ig.add(await fs.readFile(giPath, "utf8"));
    }
    this.ig.add([
      "node_modules/",
      "dist/",
      "build/",
      ".cache/",
      "*.log",
      "*.tmp",
      "*.bak",
      "*.env",
      // 你可以继续按需补充
      ...this.opts.extraIgnores,
    ]);

    // 初次将工作区复制到影子工作副本（仅受控文件）
    await this.syncWorkspaceToShadow();

    // 初始化 git 仓库并进行初次提交（可选）
    this.git = simpleGit({ baseDir: this.shadowRepoDir });
    if (!(await fse.pathExists(path.join(this.shadowRepoDir, ".git")))) {
      await this.git.init();
      await this.git.addConfig("user.name", "ContinueBot");
      await this.git.addConfig("user.email", "<EMAIL>");
    }

    if (this.opts.initSnapshot) {
      await this.commit("Initial checkpoint");
    }
  }

  /** 开始监听文件变化（增量+防抖） */
  async start() {
    this.watcher = chokidar.watch(this.opts.workspaceDir, {
      ignored: (p) => this.ig.ignores(path.relative(this.opts.workspaceDir, p)),
      ignoreInitial: true,
      awaitWriteFinish: { stabilityThreshold: 200, pollInterval: 100 },
    });

    const onChange = () => {
      this.pending = true;
      clearTimeout(this.timer);
      this.timer = setTimeout(async () => {
        if (this.pending) {
          this.pending = false;
          await this.createCheckpoint("Auto checkpoint (file changes)");
        }
      }, this.opts.debounceMs);
    };

    this.watcher
      .on("add", onChange)
      .on("change", onChange)
      .on("unlink", onChange)
      .on("addDir", onChange)
      .on("unlinkDir", onChange);
  }

  /** 停止监听 */
  async stop() {
    await this.watcher?.close();
  }

  /** 外部触发型检查点：如“运行命令前/后”、“应用一批编辑后” */
  async createCheckpoint(message: string) {
    await this.syncWorkspaceToShadow();
    await this.commit(message);
  }

  /** 列出提交（检查点） */
  async listCheckpoints(limit = 50) {
    const logs = await this.git.log({ maxCount: limit });
    return logs.all.map((c) => ({ hash: c.hash, date: c.date, message: c.message }));
  }

  /** 预览某检查点与当前工作区差异（返回文本 diff 或文件列表） */
  async diffWithHead(hash: string) {
    return this.q.add(async () => {
      return this.git.diff([`${hash}...HEAD`]);
    });
  }

  /** 回滚：把影子仓某次提交的内容覆盖回真实工作区 */
  async restore(hash: string) {
    await this.q.add(async () => {
      await this.git.checkout(hash);
      // 用影子工作副本内容覆盖真实工作区（遵循忽略规则）
      await this.copyDir(this.shadowRepoDir, this.opts.workspaceDir, true);
      // 切回 HEAD
      await this.git.checkout("HEAD");
    });
  }

  /** —— 内部 —— */

  private async commit(message: string) {
    return this.q.add(async () => {
      await this.git.add("-A"); // 记录增删改重命名
      const status = await this.git.status();
      if (
        status.created.length ||
        status.modified.length ||
        status.deleted.length ||
        status.renamed.length
      ) {
        await this.git.commit(message);
      }
    });
  }

  private async syncWorkspaceToShadow() {
    // 仅复制“未被忽略”的文件到影子工作副本
    await this.copyDir(this.opts.workspaceDir, this.shadowRepoDir, false);
  }

  private async copyDir(src: string, dest: string, overwriteDest = false) {
    await fse.ensureDir(dest);
    const entries = await fs.readdir(src, { withFileTypes: true });
    for (const e of entries) {
      const rel = e.name;
      const relPath = path.relative(this.opts.workspaceDir, path.join(src, rel));
      if (rel === ".git") continue; // 不带入主仓库 .git
      if (this.ig.ignores(relPath)) continue;

      const s = path.join(src, rel);
      const d = path.join(dest, rel);
      if (e.isDirectory()) {
        await this.copyDir(s, d, overwriteDest);
      } else if (e.isFile()) {
        await fse.ensureDir(path.dirname(d));
        if (overwriteDest || !(await fse.pathExists(d))) {
          await fse.copy(s, d, { overwrite: true, errorOnExist: false });
        } else {
          // 小优化：仅当内容实际变化时复制
          const [a, b] = await Promise.all([fse.readFile(s), fse.readFile(d).catch(() => Buffer.alloc(0))]);
          if (!a.equals(b)) await fse.copy(s, d, { overwrite: true });
        }
      }
    }
  }
}
```

**要点说明**

* 影子仓**独立目录**（默认 `~/.continue/checkpoints/<sessionId>`），不污染主项目。与 Roo 将影子仓放到“全局存储/任务目录”的理念一致。 ([GitHub][2])
* **监听 + 防抖**：把短时间内的多次改动合并为一个检查点，降低提交频率。 ([Roo Code Docs][1])
* **串行队列**：保证 Git 操作严格顺序，避免竞争。
* **遵循 `.gitignore` + 内置忽略**：显著降低磁盘和噪声。Roo 的经验也强调遵循忽略规则与控制体积。 ([Roo Code Docs][1], [GitHub][2])
* **恢复**：用影子工作副本覆盖真实工作区（贴近 Roo 的“在影子仓 reset/checkout，再把文件拷回工作区”的做法）。 ([Roo Code Docs][1])

---

# 三、在 Continue 中的接入点（无侵入集成）

Continue 是 VS Code 扩展 + Core 服务架构，可通过“**应用编辑**（apply role）”和**工具/命令**管线挂钩：

* **会话开始**：创建 `ShadowCheckpointService(sessionId).init()`，可在启动新 Agent/Task 时做一次“初始快照”。
* **应用编辑后**：当 Continue 的“Apply”角色将 diff 写入文件后，调用 `createCheckpoint("Applied edits")`。
* **运行命令前/后**：在执行“编译/测试/脚本”前后各打一个检查点，便于失败回滚。
  这些点与 Continue 的“应用编辑/模型角色与工作流”文档契合。 ([Continue][3], [Continue][4], [Continue AI][5])

**示例（伪代码，放在你的 Continue Core 扩展层）：**

```ts
import { ShadowCheckpointService } from "./checkpoints/ShadowCheckpointService";

const cps = new ShadowCheckpointService({
  workspaceDir: getWorkspacePath(),     // 你的工作区路径
  sessionId: getCurrentSessionId(),     // 来自 Continue 的会话标识
  initSnapshot: true,
});

await cps.init();
await cps.start();

// 1) 模型“应用编辑”后
onApplyEdits(async () => {
  await cps.createCheckpoint("Applied edits");
});

// 2) 运行命令前后
onWillRunCommand(async (cmd) => {
  await cps.createCheckpoint(`Before: ${cmd}`);
});
onDidRunCommand(async (cmd, ok) => {
  await cps.createCheckpoint(`After: ${cmd} (${ok ? "ok" : "fail"})`);
});

// 3) 在 UI/命令面板中暴露
registerCommand("continue.checkpoints.list", async () => showQuickPick(await cps.listCheckpoints()));
registerCommand("continue.checkpoints.restore", async (hash) => {
  const confirmed = await confirm("Restoring will overwrite current files. Continue?");
  if (confirmed) await cps.restore(hash);
});
```

> 如果你在 Continue 侧已经有“文件落盘/应用补丁”的集中入口，只需要在**落盘之后**调用一次 `createCheckpoint` 即可。Continue 官方“Apply Role”页面说明了“模型用于生成更精确的 diff 以应用到文件”，这正是检查点触发的最佳时机。 ([Continue][3])

---

# 四、配置 & 运维建议

1. **目录与清理**

* 默认影子仓根目录 `~/.continue/checkpoints`。建议提供 `continue.checkpoints.baseDir` 配置项，允许用户自定义磁盘分区。
* 提供“清理历史会话影子仓”的命令（按时间或数量保留），避免 Roo 社区里反映的**磁盘爆涨**问题。也可做阈值警报（> X GB 给出提示）。 ([GitHub][2])

2. **忽略规则**

* 默认启用 `.gitignore`。另外内置忽略如 `node_modules/`、构建产物、缓存、日志、.env 等（可让用户增删）。这与 Roo 的做法一致，能显著降低仓库体积。 ([Roo Code Docs][1])

3. **性能**

* Git 操作放在 `PQueue` 串行队列执行；监听防抖（500ms 可调）；大文件 >N MB 直接跳过并提示。
* Diff 展示建议用 VS Code 内置 diff 视图（在扩展层打开），避免前端渲染超大差异卡顿。

4. **健壮性**

* 恢复前弹窗确认；必要时先备份当前工作区到临时目录再覆盖。
* 影子仓损坏/提交失败：捕获异常并自动“重建影子仓”，提示用户重试。
* 注意**并行会话**：不同 `sessionId` 使用不同影子仓路径即可隔离。

---

# 五、如果你想**最大限度复用 Roo 的做法**（迁移点对点对照）

* **结构理念**：Roo “任务级影子仓 + 自动检查点 + 一键回滚”是本方案的骨架；我们用 `simple-git`/`chokidar` 在 Node/Core 复刻。 ([Roo Code Docs][1])
* **存储位置**：Roo 把每个任务仓库放在 VS Code globalStorage 下的 `.../rooveterinaryinc.roo-cline/tasks`。我们改为 `~/.continue/checkpoints/<sessionId>`，避免 VS Code 依赖，更易跨 IDE。 ([GitHub][2])
* **触发时机**：Roo 在“任务开始、文件修改前/后、命令执行前”保存检查点；我们在 Continue 的“Apply 落盘后”和“命令前后”一致触发。 ([Roo Code Docs][1])
* **风险对照**：Roo 社区有关于**磁盘暴涨**与**恢复可靠性**的 issue；因此我们默认严格忽略、提供清理命令、恢复前确认/备份。 ([GitHub][2])

---

# 六、最小可用接入（Checklist）

* [ ] 在 Core 层加入 `ShadowCheckpointService` 源码；
* [ ] 在 Continue 的会话/Agent 启动时 `init()+start()`；
* [ ] 在“应用编辑后”与“命令前后”调用 `createCheckpoint()`；
* [ ] 暴露两个命令：**列出检查点**、**恢复到检查点**；
* [ ] 提供 `baseDir / debounceMs / extraIgnores / maxSizeMB` 等配置项；
* [ ] 提供“清理旧检查点”命令与空间阈值提示。

完成以上六步，你就拥有了与 Roo Code 类似的**检查点/回滚**能力，并且可在 Continue 的工作流中稳态运行。

---

## 参考（核心依据）

* Roo Code 官方“Checkpoints”文档：影子 Git、自动快照、回滚/对比的工作方式与触发时机。 ([Roo Code Docs][1])
* Roo 社区 Issues：影子仓**磁盘占用**、清理与持久化路径的实践与风险。 ([GitHub][2])
* Continue 文档/页面：扩展技术栈与“Apply Role（应用编辑）”时机，便于挂钩检查点。 ([Continue][3], [Continue][4], [Continue AI][5])

如果你愿意，我也可以把上述服务封装成一个 npm 包（比如 `@your-org/shadow-checkpoints`），顺带给出 VS Code/Continue 的命令与配置样例。

[1]: https://docs.roocode.com/features/checkpoints/?utm_source=chatgpt.com "Checkpoints | Roo Code Documentation"
[2]: https://github.com/RooCodeInc/Roo-Code/issues/3080?utm_source=chatgpt.com "Checkpoints creating excessive disk usage (40GB+) in VSCode ... - GitHub"
[3]: https://docs.continue.dev/customize/model-roles/apply?utm_source=chatgpt.com "Apply Role - Continue"
[4]: https://hub.continue.dev/continuedev/vscode?utm_source=chatgpt.com "Continue VS Code extension assistant"
[5]: https://docs.continue.org.cn/?utm_source=chatgpt.com "介绍 | Continue AI 代码助手"
