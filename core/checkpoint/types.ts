/**
 * Checkpoint 相关的类型定义
 */

export interface CheckpointMetadata {
  /** 检查点唯一标识 */
  id: string;
  /** 关联的会话ID */
  sessionId: string;
  /** 对话ID（单轮对话标识） */
  conversationId?: string;
  /** 消息ID（具体消息标识） */
  messageId?: string;
  /** 检查点创建时间 */
  timestamp: number;
  /** 检查点描述信息 */
  message: string;
  /** 作者信息 */
  author?: {
    name: string;
    email?: string;
  };
  /** 自定义标签 */
  tags?: string[];
  /** 关联任务号 */
  taskId?: string;
  /** Git commit hash */
  commitHash: string;
  /** 工作区路径 */
  workspaceDir: string;
  /** 影子仓库路径 */
  shadowRepoDir: string;
  /** 检查点类型 */
  type: CheckpointType;
  /** 扩展的自定义字段 */
  customFields?: Record<string, any>;
}

export enum CheckpointType {
  /** 自动创建的检查点（文件变化触发） */
  AUTO = "auto",
  /** 手动创建的检查点 */
  MANUAL = "manual",
  /** 对话完成后的检查点 */
  CONVERSATION_END = "conversation_end",
  /** 会话开始的初始检查点 */
  SESSION_START = "session_start",
  /** 应用编辑后的检查点 */
  APPLY_EDITS = "apply_edits",
  /** 命令执行前的检查点 */
  BEFORE_COMMAND = "before_command",
  /** 命令执行后的检查点 */
  AFTER_COMMAND = "after_command",
}

export interface CheckpointOptions {
  /** 工作区目录 */
  workspaceDir: string;
  /** 会话ID */
  sessionId: string;
  /** 影子仓库根目录（默认~/.aimi/checkpoints） */
  baseDir?: string;
  /** 文件变化防抖时间（毫秒） */
  debounceMs?: number;
  /** 额外的忽略模式 */
  extraIgnores?: string[];
  /** 是否在初始化时创建快照 */
  initSnapshot?: boolean;
  /** 最大保留的检查点数量 */
  maxCheckpoints?: number;
  /** 检查点保留天数 */
  retentionDays?: number;
  /** 作者信息 */
  author?: {
    name: string;
    email?: string;
  };
}

export interface CheckpointDiff {
  /** 文件路径 */
  filePath: string;
  /** 变更类型 */
  changeType: "added" | "modified" | "deleted" | "renamed";
  /** 变更内容（diff格式） */
  diff?: string;
  /** 旧文件路径（重命名时） */
  oldPath?: string;
}

export interface CheckpointRestoreOptions {
  /** 是否备份当前状态 */
  backup?: boolean;
  /** 备份目录 */
  backupDir?: string;
  /** 是否强制覆盖 */
  force?: boolean;
  /** 要恢复的文件列表（为空则恢复所有） */
  files?: string[];
}

export interface CheckpointListOptions {
  /** 限制返回数量 */
  limit?: number;
  /** 偏移量 */
  offset?: number;
  /** 按类型过滤 */
  type?: CheckpointType;
  /** 按标签过滤 */
  tags?: string[];
  /** 开始时间 */
  startTime?: number;
  /** 结束时间 */
  endTime?: number;
}

export interface CheckpointStats {
  /** 总检查点数量 */
  totalCount: number;
  /** 占用磁盘空间（字节） */
  diskUsage: number;
  /** 最早检查点时间 */
  earliestTimestamp?: number;
  /** 最新检查点时间 */
  latestTimestamp?: number;
  /** 按类型分组的统计 */
  byType: Record<CheckpointType, number>;
}

export interface CheckpointCleanupOptions {
  /** 保留最近N个检查点 */
  keepRecent?: number;
  /** 保留N天内的检查点 */
  keepDays?: number;
  /** 按类型保留策略 */
  keepByType?: Partial<Record<CheckpointType, number>>;
  /** 是否执行清理（false为预览模式） */
  execute?: boolean;
}

export interface CheckpointCleanupResult {
  /** 将被删除的检查点列表 */
  toDelete: CheckpointMetadata[];
  /** 释放的磁盘空间（字节） */
  freedSpace: number;
  /** 是否已执行清理 */
  executed: boolean;
}
