import * as path from "node:path";
import * as os from "node:os";
import { promises as fs } from "node:fs";
import * as fse from "fs-extra";
import chokidar, { FSWatcher } from "chokidar";
import simpleGit, { SimpleGit } from "simple-git";
import PQueue from "p-queue";
import { v4 as uuidV4 } from "uuid";

import { IgnoreManager } from "./ignoreManager.js";
import {
  CheckpointCleanupOptions,
  CheckpointCleanupResult,
  CheckpointDiff,
  CheckpointListOptions,
  CheckpointMetadata,
  CheckpointOptions,
  CheckpointRestoreOptions,
  CheckpointStats,
  CheckpointType,
} from "./types.js";

/**
 * ShadowGit 检查点服务
 * 基于影子 Git 仓库实现的检查点管理系统
 */
export class ShadowCheckpointService {
  private opts: Required<CheckpointOptions>;
  private readonly shadowRoot: string;
  private readonly shadowRepoDir: string;
  private readonly metadataFile: string;
  private git!: SimpleGit;
  private ignoreManager: IgnoreManager;
  private queue = new PQueue({ concurrency: 1 }); // 串行化 Git 操作
  private watcher?: FSWatcher;
  private pending = false;
  private timer?: NodeJS.Timeout;
  private checkpoints: CheckpointMetadata[] = [];

  constructor(opts: CheckpointOptions) {
    this.opts = {
      baseDir: path.join(os.homedir(), ".aimi", "checkpoints"),
      debounceMs: 500,
      extraIgnores: [],
      initSnapshot: true,
      maxCheckpoints: 100,
      retentionDays: 30,
      author: { name: "AIMI Bot", email: "<EMAIL>" },
      ...opts,
    };

    this.shadowRoot = this.opts.baseDir;
    this.shadowRepoDir = path.join(this.shadowRoot, this.opts.sessionId);
    this.metadataFile = path.join(this.shadowRepoDir, ".aimi_checkpoints.json");
    this.ignoreManager = new IgnoreManager(this.opts.workspaceDir);
  }

  /**
   * 初始化影子仓库
   */
  async init(): Promise<void> {
    await fse.ensureDir(this.shadowRepoDir);

    // 初始化忽略管理器
    await this.ignoreManager.initialize(this.opts.extraIgnores);

    // 初次将工作区复制到影子工作副本
    await this.syncWorkspaceToShadow();

    // 初始化 Git 仓库
    this.git = simpleGit({ baseDir: this.shadowRepoDir });
    if (!(await fse.pathExists(path.join(this.shadowRepoDir, ".git")))) {
      await this.git.init();
      await this.git.addConfig("user.name", this.opts.author.name);
      await this.git.addConfig(
        "user.email",
        this.opts.author.email || "<EMAIL>",
      );
    }

    // 加载现有的检查点元数据
    await this.loadCheckpoints();

    // 创建初始检查点
    if (this.opts.initSnapshot) {
      await this.createCheckpoint(
        "Initial checkpoint",
        CheckpointType.SESSION_START,
      );
    }
  }

  /**
   * 开始监听文件变化
   */
  async start(): Promise<void> {
    this.watcher = chokidar.watch(this.opts.workspaceDir, {
      ignored: (filePath) => this.ignoreManager.shouldIgnore(filePath),
      ignoreInitial: true,
      awaitWriteFinish: { stabilityThreshold: 200, pollInterval: 100 },
      atomic: true,
    });

    const onChange = () => {
      this.pending = true;
      clearTimeout(this.timer);
      this.timer = setTimeout(async () => {
        if (this.pending) {
          this.pending = false;
          await this.createCheckpoint(
            "Auto checkpoint (file changes)",
            CheckpointType.AUTO,
          );
        }
      }, this.opts.debounceMs);
    };

    (this.watcher as any)
      ?.on("add", onChange)
      ?.on("change", onChange)
      ?.on("unlink", onChange)
      ?.on("addDir", onChange)
      ?.on("unlinkDir", onChange);
  }

  /**
   * 停止监听
   */
  async stop(): Promise<void> {
    clearTimeout(this.timer);
    await this.watcher?.close();
  }

  /**
   * 创建检查点
   */
  async createCheckpoint(
    message: string,
    type: CheckpointType = CheckpointType.MANUAL,
    options?: {
      conversationId?: string;
      messageId?: string;
      tags?: string[];
      taskId?: string;
      customFields?: Record<string, any>;
    },
  ): Promise<CheckpointMetadata> {
    return this.queue.add(
      async (): Promise<CheckpointMetadata> => {
        // 同步工作区到影子仓库
        await this.syncWorkspaceToShadow();

        // 提交更改
        const commitHash = await this.commit(message);

        if (!commitHash) {
          throw new Error("No changes to commit");
        }

        // 创建检查点元数据
        const checkpoint: CheckpointMetadata = {
          id: uuidV4(),
          sessionId: this.opts.sessionId,
          conversationId: options?.conversationId,
          messageId: options?.messageId,
          timestamp: Date.now(),
          message,
          author: this.opts.author,
          tags: options?.tags || [],
          taskId: options?.taskId,
          commitHash,
          workspaceDir: this.opts.workspaceDir,
          shadowRepoDir: this.shadowRepoDir,
          type,
          customFields: options?.customFields,
        };

        // 添加到检查点列表
        this.checkpoints.push(checkpoint);

        // 保存元数据
        await this.saveCheckpoints();

        // 清理旧检查点
        await this.cleanupOldCheckpoints();

        return checkpoint;
      },
      { throwOnTimeout: true },
    );
  }

  /**
   * 列出检查点
   */
  async listCheckpoints(
    options: CheckpointListOptions = {},
  ): Promise<CheckpointMetadata[]> {
    let filtered = [...this.checkpoints];

    // 按类型过滤
    if (options.type) {
      filtered = filtered.filter((cp) => cp.type === options.type);
    }

    // 按标签过滤
    if (options.tags && options.tags.length > 0) {
      filtered = filtered.filter((cp) =>
        options.tags!.some((tag) => cp.tags?.includes(tag)),
      );
    }

    // 按时间范围过滤
    if (options.startTime) {
      filtered = filtered.filter((cp) => cp.timestamp >= options.startTime!);
    }
    if (options.endTime) {
      filtered = filtered.filter((cp) => cp.timestamp <= options.endTime!);
    }

    // 排序（最新的在前）
    filtered.sort((a, b) => b.timestamp - a.timestamp);

    // 分页
    const offset = options.offset || 0;
    const limit = options.limit || filtered.length;

    return filtered.slice(offset, offset + limit);
  }

  /**
   * 获取检查点详情
   */
  async getCheckpoint(
    checkpointId: string,
  ): Promise<CheckpointMetadata | null> {
    return this.checkpoints.find((cp) => cp.id === checkpointId) || null;
  }

  /**
   * 获取检查点差异
   */
  async getDiff(
    checkpointId: string,
    compareWith?: string,
  ): Promise<CheckpointDiff[]> {
    const checkpoint = await this.getCheckpoint(checkpointId);
    if (!checkpoint) {
      throw new Error(`Checkpoint ${checkpointId} not found`);
    }

    const compareHash = compareWith
      ? (await this.getCheckpoint(compareWith))?.commitHash
      : "HEAD";

    if (!compareHash) {
      throw new Error(`Compare target not found`);
    }

    return this.queue.add(
      async (): Promise<CheckpointDiff[]> => {
        const diffText = await this.git.diff([
          `${checkpoint.commitHash}...${compareHash}`,
        ]);
        return this.parseDiff(diffText);
      },
      { throwOnTimeout: true },
    );
  }

  /**
   * 恢复到指定检查点
   */
  async restore(
    checkpointId: string,
    options: CheckpointRestoreOptions = {},
  ): Promise<void> {
    const checkpoint = await this.getCheckpoint(checkpointId);
    if (!checkpoint) {
      throw new Error(`Checkpoint ${checkpointId} not found`);
    }

    return this.queue.add(async (): Promise<void> => {
      // 备份当前状态
      if (options.backup) {
        const backupDir =
          options.backupDir ||
          path.join(this.shadowRoot, `backup_${Date.now()}`);
        await this.backupCurrentState(backupDir);
      }

      // 切换到指定提交
      await this.git.checkout(checkpoint.commitHash);

      // 恢复文件到工作区
      await this.restoreToWorkspace(options.files);

      // 切回 HEAD
      await this.git.checkout("HEAD");
    });
  }

  /**
   * 删除检查点
   */
  async deleteCheckpoint(checkpointId: string): Promise<void> {
    const index = this.checkpoints.findIndex((cp) => cp.id === checkpointId);
    if (index === -1) {
      throw new Error(`Checkpoint ${checkpointId} not found`);
    }

    this.checkpoints.splice(index, 1);
    await this.saveCheckpoints();
  }

  /**
   * 获取检查点统计信息
   */
  async getStats(): Promise<CheckpointStats> {
    const stats: CheckpointStats = {
      totalCount: this.checkpoints.length,
      diskUsage: 0,
      byType: {} as Record<CheckpointType, number>,
    };

    // 计算磁盘使用量
    try {
      const dirStats = await this.getDirSize(this.shadowRepoDir);
      stats.diskUsage = dirStats;
    } catch (error) {
      // 忽略错误
    }

    // 统计时间范围
    if (this.checkpoints.length > 0) {
      const timestamps = this.checkpoints.map((cp) => cp.timestamp).sort();
      stats.earliestTimestamp = timestamps[0];
      stats.latestTimestamp = timestamps[timestamps.length - 1];
    }

    // 按类型统计
    for (const type of Object.values(CheckpointType)) {
      stats.byType[type] = this.checkpoints.filter(
        (cp) => cp.type === type,
      ).length;
    }

    return stats;
  }

  /**
   * 清理检查点
   */
  async cleanup(
    options: CheckpointCleanupOptions,
  ): Promise<CheckpointCleanupResult> {
    const toDelete: CheckpointMetadata[] = [];
    const now = Date.now();

    // 按时间清理
    if (options.keepDays) {
      const cutoffTime = now - options.keepDays * 24 * 60 * 60 * 1000;
      toDelete.push(
        ...this.checkpoints.filter((cp) => cp.timestamp < cutoffTime),
      );
    }

    // 按数量清理
    if (options.keepRecent) {
      const sorted = [...this.checkpoints].sort(
        (a, b) => b.timestamp - a.timestamp,
      );
      toDelete.push(...sorted.slice(options.keepRecent));
    }

    // 按类型清理
    if (options.keepByType) {
      for (const [type, keepCount] of Object.entries(options.keepByType)) {
        const ofType = this.checkpoints
          .filter((cp) => cp.type === (type as CheckpointType))
          .sort((a, b) => b.timestamp - a.timestamp);

        if (ofType.length > keepCount) {
          toDelete.push(...ofType.slice(keepCount));
        }
      }
    }

    // 去重
    const uniqueToDelete = Array.from(new Set(toDelete));

    let freedSpace = 0;
    if (options.execute) {
      for (const checkpoint of uniqueToDelete) {
        await this.deleteCheckpoint(checkpoint.id);
      }
      // 估算释放的空间（简化计算）
      freedSpace = uniqueToDelete.length * 1024 * 1024; // 假设每个检查点平均1MB
    }

    return {
      toDelete: uniqueToDelete,
      freedSpace,
      executed: options.execute || false,
    };
  }

  // ========== 私有方法 ==========

  /**
   * 提交更改到 Git
   */
  private async commit(message: string): Promise<string | null> {
    await this.git.add("-A");
    const status = await this.git.status();

    if (
      status.created.length ||
      status.modified.length ||
      status.deleted.length ||
      status.renamed.length
    ) {
      const result = await this.git.commit(message);
      return result.commit;
    }

    return null;
  }

  /**
   * 同步工作区到影子仓库
   */
  private async syncWorkspaceToShadow(): Promise<void> {
    await this.copyDir(this.opts.workspaceDir, this.shadowRepoDir, false);
  }

  /**
   * 复制目录
   */
  private async copyDir(
    src: string,
    dest: string,
    overwriteDest = false,
  ): Promise<void> {
    await fse.ensureDir(dest);
    const entries = await fs.readdir(src, { withFileTypes: true });

    for (const entry of entries) {
      const srcPath = path.join(src, entry.name);
      const destPath = path.join(dest, entry.name);

      // 跳过 .git 目录
      if (entry.name === ".git") continue;

      // 检查是否应该忽略
      if (this.ignoreManager.shouldIgnore(srcPath)) continue;

      if (entry.isDirectory()) {
        await this.copyDir(srcPath, destPath, overwriteDest);
      } else if (entry.isFile()) {
        await fse.ensureDir(path.dirname(destPath));

        if (overwriteDest || !(await fse.pathExists(destPath))) {
          await fse.copy(srcPath, destPath, { overwrite: true });
        } else {
          // 只有内容变化时才复制
          const [srcContent, destContent] = await Promise.all([
            fse.readFile(srcPath),
            fse.readFile(destPath).catch(() => Buffer.alloc(0)),
          ]);

          if (srcContent !== destContent) {
            await fse.copy(srcPath, destPath, { overwrite: true });
          }
        }
      }
    }
  }

  /**
   * 加载检查点元数据
   */
  private async loadCheckpoints(): Promise<void> {
    try {
      if (await fse.pathExists(this.metadataFile)) {
        const content = await fs.readFile(this.metadataFile, "utf8");
        this.checkpoints = JSON.parse(content);
      }
    } catch (error) {
      console.warn("Failed to load checkpoints metadata:", error);
      this.checkpoints = [];
    }
  }

  /**
   * 保存检查点元数据
   */
  private async saveCheckpoints(): Promise<void> {
    await fse.ensureDir(path.dirname(this.metadataFile));
    await fs.writeFile(
      this.metadataFile,
      JSON.stringify(this.checkpoints, null, 2),
      "utf8",
    );
  }

  /**
   * 清理旧检查点
   */
  private async cleanupOldCheckpoints(): Promise<void> {
    if (this.checkpoints.length <= this.opts.maxCheckpoints) {
      return;
    }

    // 按时间排序，保留最新的
    const sorted = [...this.checkpoints].sort(
      (a, b) => b.timestamp - a.timestamp,
    );
    const toKeep = sorted.slice(0, this.opts.maxCheckpoints);

    this.checkpoints = toKeep;
    await this.saveCheckpoints();
  }

  /**
   * 解析 Git diff 输出
   */
  private parseDiff(diffText: string): CheckpointDiff[] {
    const diffs: CheckpointDiff[] = [];
    const lines = diffText.split("\n");

    let currentFile: CheckpointDiff | null = null;

    for (const line of lines) {
      if (line.startsWith("diff --git")) {
        // 新文件开始
        const match = line.match(/diff --git a\/(.+) b\/(.+)/);
        if (match) {
          currentFile = {
            filePath: match[2],
            changeType: "modified",
            diff: "",
          };
          diffs.push(currentFile);
        }
      } else if (line.startsWith("new file mode")) {
        if (currentFile) currentFile.changeType = "added";
      } else if (line.startsWith("deleted file mode")) {
        if (currentFile) currentFile.changeType = "deleted";
      } else if (line.startsWith("rename from")) {
        if (currentFile) {
          currentFile.changeType = "renamed";
          currentFile.oldPath = line.substring("rename from ".length);
        }
      } else if (
        currentFile &&
        (line.startsWith("+") || line.startsWith("-") || line.startsWith(" "))
      ) {
        currentFile.diff += line + "\n";
      }
    }

    return diffs;
  }

  /**
   * 备份当前状态
   */
  private async backupCurrentState(backupDir: string): Promise<void> {
    await fse.ensureDir(backupDir);
    await this.copyDir(this.opts.workspaceDir, backupDir, true);
  }

  /**
   * 恢复文件到工作区
   */
  private async restoreToWorkspace(files?: string[]): Promise<void> {
    if (files && files.length > 0) {
      // 只恢复指定文件
      for (const file of files) {
        const srcPath = path.join(this.shadowRepoDir, file);
        const destPath = path.join(this.opts.workspaceDir, file);

        if (await fse.pathExists(srcPath)) {
          await fse.ensureDir(path.dirname(destPath));
          await fse.copy(srcPath, destPath, { overwrite: true });
        }
      }
    } else {
      // 恢复所有文件
      await this.copyDir(this.shadowRepoDir, this.opts.workspaceDir, true);
    }
  }

  /**
   * 获取目录大小
   */
  private async getDirSize(dirPath: string): Promise<number> {
    let size = 0;

    try {
      const entries = await fs.readdir(dirPath, { withFileTypes: true });

      for (const entry of entries) {
        const fullPath = path.join(dirPath, entry.name);

        if (entry.isDirectory()) {
          size += await this.getDirSize(fullPath);
        } else if (entry.isFile()) {
          const stats = await fs.stat(fullPath);
          size += stats.size;
        }
      }
    } catch (error) {
      // 忽略错误
    }

    return size;
  }
}
