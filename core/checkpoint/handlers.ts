import { IDE } from "../";
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "../protocol/messenger";
import { CheckpointManager } from "./CheckpointManager";
import { CheckpointType } from "./types";
import type { FromCoreProtocol, ToCoreProtocol } from "../protocol";

/**
 * Checkpoint 协议处理器
 * 处理来自 IDE 和 Webview 的 checkpoint 相关请求
 */
export class CheckpointHandlers {
  private checkpointManager: CheckpointManager;

  constructor(ide: IDE) {
    this.checkpointManager = new CheckpointManager(ide);
  }

  /**
   * 初始化处理器
   */
  async init(): Promise<void> {
    await this.checkpointManager.init();
  }

  /**
   * 注册所有 checkpoint 相关的协议处理器
   */
  registerHandlers(
    messenger: IMessenger<ToCoreProtocol, FromCoreProtocol>,
  ): void {
    // 创建检查点
    messenger.on("checkpoint/create", async (msg) => {
      const {
        message,
        type,
        conversationId,
        messageId,
        tags,
        taskId,
        customFields,
      } = msg.data;

      // 获取当前会话ID（这里需要从某个地方获取，可能需要传入或从全局状态获取）
      const sessionId = await this.getCurrentSessionId();

      const checkpointType = type
        ? (type as CheckpointType)
        : CheckpointType.MANUAL;

      const checkpoint = await this.checkpointManager.createCheckpoint(
        sessionId,
        message,
        checkpointType,
        {
          conversationId,
          messageId,
          tags,
          taskId,
          customFields,
        },
      );

      return {
        id: checkpoint.id,
        commitHash: checkpoint.commitHash,
        timestamp: checkpoint.timestamp,
      };
    });

    // 列出检查点
    messenger.on("checkpoint/list", async (msg) => {
      const sessionId = await this.getCurrentSessionId();
      const checkpoints = await this.checkpointManager.listCheckpoints(
        sessionId,
        msg.data,
      );

      return checkpoints.map((cp) => ({
        id: cp.id,
        sessionId: cp.sessionId,
        conversationId: cp.conversationId,
        messageId: cp.messageId,
        timestamp: cp.timestamp,
        message: cp.message,
        author: cp.author,
        tags: cp.tags,
        taskId: cp.taskId,
        commitHash: cp.commitHash,
        type: cp.type,
        customFields: cp.customFields,
      }));
    });

    // 获取检查点详情
    messenger.on("checkpoint/get", async (msg) => {
      const sessionId = await this.getCurrentSessionId();
      const checkpoint = await this.checkpointManager.getCheckpoint(
        sessionId,
        msg.data.id,
      );

      return checkpoint
        ? {
            id: checkpoint.id,
            sessionId: checkpoint.sessionId,
            conversationId: checkpoint.conversationId,
            messageId: checkpoint.messageId,
            timestamp: checkpoint.timestamp,
            message: checkpoint.message,
            author: checkpoint.author,
            tags: checkpoint.tags,
            taskId: checkpoint.taskId,
            commitHash: checkpoint.commitHash,
            workspaceDir: checkpoint.workspaceDir,
            shadowRepoDir: checkpoint.shadowRepoDir,
            type: checkpoint.type,
            customFields: checkpoint.customFields,
          }
        : null;
    });

    // 删除检查点
    messenger.on("checkpoint/delete", async (msg) => {
      const sessionId = await this.getCurrentSessionId();
      await this.checkpointManager.deleteCheckpoint(sessionId, msg.data.id);
    });

    // 恢复检查点
    messenger.on("checkpoint/restore", async (msg) => {
      const sessionId = await this.getCurrentSessionId();
      await this.checkpointManager.restoreCheckpoint(sessionId, msg.data.id, {
        backup: msg.data.backup,
        backupDir: msg.data.backupDir,
        force: msg.data.force,
        files: msg.data.files,
      });
    });

    // 获取检查点差异
    messenger.on("checkpoint/diff", async (msg) => {
      const sessionId = await this.getCurrentSessionId();
      const diffs = await this.checkpointManager.getCheckpointDiff(
        sessionId,
        msg.data.id,
        msg.data.compareWith,
      );

      return diffs.map((diff) => ({
        filePath: diff.filePath,
        changeType: diff.changeType,
        diff: diff.diff,
        oldPath: diff.oldPath,
      }));
    });

    // 获取检查点统计信息
    messenger.on("checkpoint/stats", async (msg) => {
      const sessionId = await this.getCurrentSessionId();
      const stats = await this.checkpointManager.getStats(sessionId);

      return {
        totalCount: stats.totalCount,
        diskUsage: stats.diskUsage,
        earliestTimestamp: stats.earliestTimestamp,
        latestTimestamp: stats.latestTimestamp,
        byType: stats.byType,
      };
    });

    // 清理检查点
    messenger.on("checkpoint/cleanup", async (msg) => {
      const sessionId = await this.getCurrentSessionId();
      const result = await this.checkpointManager.cleanup(sessionId, {
        keepRecent: msg.data.keepRecent,
        keepDays: msg.data.keepDays,
        keepByType: msg.data.keepByType,
        execute: msg.data.execute,
      });

      return {
        toDelete: result.toDelete.map((cp: any) => ({
          id: cp.id,
          timestamp: cp.timestamp,
          message: cp.message,
          type: cp.type,
        })),
        freedSpace: result.freedSpace,
        executed: result.executed,
      };
    });
  }

  /**
   * 获取当前会话ID
   * 这里需要根据实际的会话管理机制来实现
   */
  private async getCurrentSessionId(): Promise<string> {
    // TODO: 实现获取当前会话ID的逻辑
    // 可能需要从全局状态、消息上下文或其他地方获取
    return "default-session";
  }

  /**
   * 在对话完成时触发检查点创建
   */
  async onConversationEnd(
    sessionId: string,
    conversationId: string,
    messageId?: string,
  ): Promise<void> {
    try {
      await this.checkpointManager.onConversationEnd(
        sessionId,
        conversationId,
        messageId,
      );
    } catch (error) {
      console.error("Failed to create conversation end checkpoint:", error);
    }
  }

  /**
   * 在应用编辑后触发检查点创建
   */
  async onApplyEdits(
    sessionId: string,
    conversationId?: string,
    messageId?: string,
    editDescription?: string,
  ): Promise<void> {
    try {
      await this.checkpointManager.onApplyEdits(
        sessionId,
        conversationId,
        messageId,
        editDescription,
      );
    } catch (error) {
      console.error("Failed to create apply edits checkpoint:", error);
    }
  }

  /**
   * 在命令执行前触发检查点创建
   */
  async onBeforeCommand(
    sessionId: string,
    command: string,
    conversationId?: string,
  ): Promise<void> {
    try {
      await this.checkpointManager.onBeforeCommand(
        sessionId,
        command,
        conversationId,
      );
    } catch (error) {
      console.error("Failed to create before command checkpoint:", error);
    }
  }

  /**
   * 在命令执行后触发检查点创建
   */
  async onAfterCommand(
    sessionId: string,
    command: string,
    success: boolean,
    conversationId?: string,
  ): Promise<void> {
    try {
      await this.checkpointManager.onAfterCommand(
        sessionId,
        command,
        success,
        conversationId,
      );
    } catch (error) {
      console.error("Failed to create after command checkpoint:", error);
    }
  }

  /**
   * 停止所有检查点服务
   */
  async shutdown(): Promise<void> {
    await this.checkpointManager.stopAll();
  }
}
