import * as path from "node:path";
import { promises as fs } from "node:fs";
import ignore, { Ignore } from "ignore";

/**
 * 忽略文件管理器
 * 负责解析和应用 .gitignore 和 .aimi_ignore 文件的规则
 */
export class IgnoreManager {
  private ig: Ignore;
  private readonly workspaceDir: string;

  constructor(workspaceDir: string) {
    this.workspaceDir = workspaceDir;
    this.ig = ignore();
  }

  /**
   * 初始化忽略规则
   * @param extraIgnores 额外的忽略模式
   */
  async initialize(extraIgnores: string[] = []): Promise<void> {
    // 重置忽略规则
    this.ig = ignore();

    // 1. 加载 .gitignore 文件
    await this.loadGitignore();

    // 2. 加载 .aimi_ignore 文件
    await this.loadAimiIgnore();

    // 3. 添加内置的忽略规则
    this.addBuiltinIgnores();

    // 4. 添加额外的忽略规则
    if (extraIgnores.length > 0) {
      this.ig.add(extraIgnores);
    }
  }

  /**
   * 加载 .gitignore 文件
   */
  private async loadGitignore(): Promise<void> {
    const gitignorePath = path.join(this.workspaceDir, ".gitignore");
    try {
      const content = await fs.readFile(gitignorePath, "utf8");
      this.ig.add(content);
    } catch (error) {
      // .gitignore 文件不存在或无法读取，忽略错误
    }
  }

  /**
   * 加载 .aimi_ignore 文件
   */
  private async loadAimiIgnore(): Promise<void> {
    const aimiIgnorePath = path.join(this.workspaceDir, ".aimi_ignore");
    try {
      const content = await fs.readFile(aimiIgnorePath, "utf8");
      this.ig.add(content);
    } catch (error) {
      // .aimi_ignore 文件不存在或无法读取，忽略错误
    }
  }

  /**
   * 添加内置的忽略规则
   */
  private addBuiltinIgnores(): void {
    const builtinIgnores = [
      // Node.js
      "node_modules/",
      "npm-debug.log*",
      "yarn-debug.log*",
      "yarn-error.log*",
      ".npm",
      ".yarn-integrity",

      // 构建产物
      "dist/",
      "build/",
      "out/",
      "target/",
      ".next/",
      ".nuxt/",

      // 缓存目录
      ".cache/",
      ".parcel-cache/",
      ".vite/",
      ".turbo/",

      // 日志文件
      "*.log",
      "logs/",

      // 临时文件
      "*.tmp",
      "*.temp",
      "*.bak",
      "*.swp",
      "*.swo",
      "*~",

      // 环境变量文件
      ".env",
      ".env.local",
      ".env.development.local",
      ".env.test.local",
      ".env.production.local",

      // IDE 和编辑器文件
      ".vscode/",
      ".idea/",
      "*.iml",
      ".project",
      ".classpath",
      ".settings/",

      // 操作系统文件
      ".DS_Store",
      "Thumbs.db",
      "desktop.ini",

      // 版本控制
      ".git/",
      ".svn/",
      ".hg/",

      // 包管理器锁文件（通常很大）
      "package-lock.json",
      "yarn.lock",
      "pnpm-lock.yaml",

      // 数据库文件
      "*.db",
      "*.sqlite",
      "*.sqlite3",

      // 媒体文件（通常很大）
      "*.mp4",
      "*.avi",
      "*.mov",
      "*.wmv",
      "*.flv",
      "*.webm",
      "*.mp3",
      "*.wav",
      "*.flac",
      "*.aac",

      // 压缩文件
      "*.zip",
      "*.rar",
      "*.7z",
      "*.tar",
      "*.tar.gz",
      "*.tar.bz2",

      // 二进制文件
      "*.exe",
      "*.dll",
      "*.so",
      "*.dylib",
      "*.bin",

      // 大型数据文件
      "*.csv",
      "*.json",
      "*.xml",
      "*.yaml",
      "*.yml",
    ].filter(
      (pattern) =>
        pattern.endsWith("/") ||
        pattern.includes("*") ||
        pattern.startsWith("."),
    );

    this.ig.add(builtinIgnores);
  }

  /**
   * 检查文件是否应该被忽略
   * @param filePath 文件路径（相对于工作区）
   * @returns 是否应该被忽略
   */
  shouldIgnore(filePath: string): boolean {
    // 转换为相对路径
    const relativePath = path.relative(this.workspaceDir, filePath);

    // 如果路径为空或者是 "."，不忽略
    if (!relativePath || relativePath === ".") {
      return false;
    }

    // 检查是否匹配忽略规则
    return this.ig.ignores(relativePath);
  }

  /**
   * 过滤文件列表，移除应该被忽略的文件
   * @param files 文件路径列表
   * @returns 过滤后的文件列表
   */
  filterFiles(files: string[]): string[] {
    return files.filter((file) => !this.shouldIgnore(file));
  }

  /**
   * 获取当前的忽略规则（用于调试）
   * @returns 忽略规则的字符串表示
   */
  getIgnoreRules(): string[] {
    // 注意：ignore 库没有直接获取规则的方法
    // 这里返回一个空数组，实际使用中可以通过其他方式记录规则
    return [];
  }

  /**
   * 添加临时忽略规则
   * @param patterns 忽略模式列表
   */
  addTemporaryIgnores(patterns: string[]): void {
    this.ig.add(patterns);
  }

  /**
   * 检查目录是否应该被忽略
   * @param dirPath 目录路径
   * @returns 是否应该被忽略
   */
  shouldIgnoreDirectory(dirPath: string): boolean {
    const relativePath = path.relative(this.workspaceDir, dirPath);

    if (!relativePath || relativePath === ".") {
      return false;
    }

    // 对于目录，确保路径以 / 结尾
    const dirPathWithSlash = relativePath.endsWith("/")
      ? relativePath
      : relativePath + "/";

    return this.ig.ignores(dirPathWithSlash) || this.ig.ignores(relativePath);
  }

  /**
   * 重新加载忽略规则
   */
  async reload(extraIgnores: string[] = []): Promise<void> {
    await this.initialize(extraIgnores);
  }
}
