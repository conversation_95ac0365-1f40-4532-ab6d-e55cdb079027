import * as path from "node:path";
import * as os from "node:os";
import { promises as fs } from "node:fs";
import * as fse from "fs-extra";

/**
 * Checkpoint 配置接口
 */
export interface CheckpointConfig {
  /** 是否启用 checkpoint 功能 */
  enabled: boolean;
  /** 影子仓库根目录 */
  baseDir: string;
  /** 文件变化防抖时间（毫秒） */
  debounceMs: number;
  /** 最大保留的检查点数量 */
  maxCheckpoints: number;
  /** 检查点保留天数 */
  retentionDays: number;
  /** 最大磁盘使用量（字节） */
  maxDiskUsage: number;
  /** 是否在会话开始时创建初始快照 */
  initSnapshot: boolean;
  /** 是否启用自动文件监听 */
  autoWatch: boolean;
  /** 作者信息 */
  author: {
    name: string;
    email?: string;
  };
  /** 额外的忽略模式 */
  extraIgnores: string[];
  /** 自动清理配置 */
  autoCleanup: {
    /** 是否启用自动清理 */
    enabled: boolean;
    /** 清理间隔（小时） */
    intervalHours: number;
    /** 清理策略 */
    strategy: {
      /** 保留最近N个检查点 */
      keepRecent?: number;
      /** 保留N天内的检查点 */
      keepDays?: number;
      /** 按类型保留策略 */
      keepByType?: Record<string, number>;
    };
  };
}

/**
 * 默认配置
 */
export const DEFAULT_CHECKPOINT_CONFIG: CheckpointConfig = {
  enabled: true,
  baseDir: path.join(os.homedir(), ".aimi", "checkpoints"),
  debounceMs: 500,
  maxCheckpoints: 100,
  retentionDays: 30,
  maxDiskUsage: 1024 * 1024 * 1024, // 1GB
  initSnapshot: true,
  autoWatch: true,
  author: {
    name: "AIMI Bot",
    email: "<EMAIL>",
  },
  extraIgnores: [],
  autoCleanup: {
    enabled: true,
    intervalHours: 24,
    strategy: {
      keepRecent: 50,
      keepDays: 7,
      keepByType: {
        session_start: 10,
        conversation_end: 20,
        apply_edits: 30,
        manual: 50,
      },
    },
  },
};

/**
 * Checkpoint 配置管理器
 */
export class CheckpointConfigManager {
  private configPath: string;
  private config: CheckpointConfig;

  constructor(configDir?: string) {
    const baseDir = configDir || path.join(os.homedir(), ".aimi");
    this.configPath = path.join(baseDir, "checkpoint-config.json");
    this.config = { ...DEFAULT_CHECKPOINT_CONFIG };
  }

  /**
   * 加载配置
   */
  async load(): Promise<CheckpointConfig> {
    try {
      if (await fse.pathExists(this.configPath)) {
        const content = await fs.readFile(this.configPath, "utf8");
        const userConfig = JSON.parse(content);
        
        // 合并用户配置和默认配置
        this.config = this.mergeConfig(DEFAULT_CHECKPOINT_CONFIG, userConfig);
      }
    } catch (error) {
      console.warn("Failed to load checkpoint config, using defaults:", error);
      this.config = { ...DEFAULT_CHECKPOINT_CONFIG };
    }

    return this.config;
  }

  /**
   * 保存配置
   */
  async save(config: Partial<CheckpointConfig>): Promise<void> {
    this.config = this.mergeConfig(this.config, config);
    
    await fse.ensureDir(path.dirname(this.configPath));
    await fs.writeFile(
      this.configPath,
      JSON.stringify(this.config, null, 2),
      "utf8"
    );
  }

  /**
   * 获取当前配置
   */
  getConfig(): CheckpointConfig {
    return { ...this.config };
  }

  /**
   * 更新配置
   */
  async updateConfig(updates: Partial<CheckpointConfig>): Promise<CheckpointConfig> {
    await this.save(updates);
    return this.getConfig();
  }

  /**
   * 重置为默认配置
   */
  async reset(): Promise<CheckpointConfig> {
    this.config = { ...DEFAULT_CHECKPOINT_CONFIG };
    await this.save(this.config);
    return this.config;
  }

  /**
   * 验证配置
   */
  validateConfig(config: Partial<CheckpointConfig>): string[] {
    const errors: string[] = [];

    if (config.debounceMs !== undefined && config.debounceMs < 0) {
      errors.push("debounceMs must be non-negative");
    }

    if (config.maxCheckpoints !== undefined && config.maxCheckpoints < 1) {
      errors.push("maxCheckpoints must be at least 1");
    }

    if (config.retentionDays !== undefined && config.retentionDays < 1) {
      errors.push("retentionDays must be at least 1");
    }

    if (config.maxDiskUsage !== undefined && config.maxDiskUsage < 1024 * 1024) {
      errors.push("maxDiskUsage must be at least 1MB");
    }

    if (config.autoCleanup?.intervalHours !== undefined && config.autoCleanup.intervalHours < 1) {
      errors.push("autoCleanup.intervalHours must be at least 1");
    }

    return errors;
  }

  /**
   * 合并配置对象
   */
  private mergeConfig(base: CheckpointConfig, updates: Partial<CheckpointConfig>): CheckpointConfig {
    const merged = { ...base };

    for (const [key, value] of Object.entries(updates)) {
      if (value !== undefined) {
        if (typeof value === "object" && !Array.isArray(value) && value !== null) {
          // 深度合并对象
          (merged as any)[key] = { ...(merged as any)[key], ...value };
        } else {
          // 直接赋值
          (merged as any)[key] = value;
        }
      }
    }

    return merged;
  }

  /**
   * 获取配置文件路径
   */
  getConfigPath(): string {
    return this.configPath;
  }

  /**
   * 检查配置是否存在
   */
  async configExists(): Promise<boolean> {
    return fse.pathExists(this.configPath);
  }

  /**
   * 导出配置
   */
  async exportConfig(exportPath: string): Promise<void> {
    await fs.writeFile(
      exportPath,
      JSON.stringify(this.config, null, 2),
      "utf8"
    );
  }

  /**
   * 导入配置
   */
  async importConfig(importPath: string): Promise<CheckpointConfig> {
    const content = await fs.readFile(importPath, "utf8");
    const importedConfig = JSON.parse(content);
    
    const errors = this.validateConfig(importedConfig);
    if (errors.length > 0) {
      throw new Error(`Invalid config: ${errors.join(", ")}`);
    }

    await this.save(importedConfig);
    return this.getConfig();
  }
}
