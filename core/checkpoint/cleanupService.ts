import { CheckpointManager } from "./CheckpointManager.js";
import { CheckpointConfigManager, CheckpointConfig } from "./config.js";
import { CheckpointType } from "./types.js";

/**
 * 检查点自动清理服务
 */
export class CheckpointCleanupService {
  private configManager: CheckpointConfigManager;
  private checkpointManager: CheckpointManager;
  private cleanupTimer?: NodeJS.Timeout;
  private isRunning = false;

  constructor(
    checkpointManager: CheckpointManager,
    configManager: CheckpointConfigManager
  ) {
    this.checkpointManager = checkpointManager;
    this.configManager = configManager;
  }

  /**
   * 启动自动清理服务
   */
  async start(): Promise<void> {
    if (this.isRunning) {
      return;
    }

    const config = await this.configManager.load();
    
    if (!config.autoCleanup.enabled) {
      console.log("Checkpoint auto cleanup is disabled");
      return;
    }

    this.isRunning = true;
    this.scheduleNextCleanup(config);
    
    console.log(`Checkpoint auto cleanup started, interval: ${config.autoCleanup.intervalHours} hours`);
  }

  /**
   * 停止自动清理服务
   */
  stop(): void {
    if (this.cleanupTimer) {
      clearTimeout(this.cleanupTimer);
      this.cleanupTimer = undefined;
    }
    this.isRunning = false;
    console.log("Checkpoint auto cleanup stopped");
  }

  /**
   * 手动执行清理
   */
  async runCleanup(sessionId?: string): Promise<{
    totalDeleted: number;
    totalFreedSpace: number;
    sessionResults: Array<{
      sessionId: string;
      deleted: number;
      freedSpace: number;
    }>;
  }> {
    const config = await this.configManager.load();
    const results = {
      totalDeleted: 0,
      totalFreedSpace: 0,
      sessionResults: [] as Array<{
        sessionId: string;
        deleted: number;
        freedSpace: number;
      }>,
    };

    try {
      if (sessionId) {
        // 清理指定会话
        const result = await this.cleanupSession(sessionId, config);
        results.sessionResults.push(result);
        results.totalDeleted += result.deleted;
        results.totalFreedSpace += result.freedSpace;
      } else {
        // 清理所有会话（这里需要获取所有活跃的会话ID）
        const sessionIds = await this.getAllSessionIds();
        
        for (const sid of sessionIds) {
          try {
            const result = await this.cleanupSession(sid, config);
            results.sessionResults.push(result);
            results.totalDeleted += result.deleted;
            results.totalFreedSpace += result.freedSpace;
          } catch (error) {
            console.error(`Failed to cleanup session ${sid}:`, error);
          }
        }
      }

      console.log(`Cleanup completed: deleted ${results.totalDeleted} checkpoints, freed ${this.formatBytes(results.totalFreedSpace)}`);
    } catch (error) {
      console.error("Cleanup failed:", error);
      throw error;
    }

    return results;
  }

  /**
   * 检查磁盘使用量并在必要时执行清理
   */
  async checkDiskUsageAndCleanup(): Promise<void> {
    const config = await this.configManager.load();
    const sessionIds = await this.getAllSessionIds();
    
    let totalDiskUsage = 0;
    
    // 计算总磁盘使用量
    for (const sessionId of sessionIds) {
      try {
        const stats = await this.checkpointManager.getStats(sessionId);
        totalDiskUsage += stats.diskUsage;
      } catch (error) {
        console.error(`Failed to get stats for session ${sessionId}:`, error);
      }
    }

    console.log(`Total checkpoint disk usage: ${this.formatBytes(totalDiskUsage)}`);

    // 如果超过限制，执行清理
    if (totalDiskUsage > config.maxDiskUsage) {
      console.log(`Disk usage exceeded limit (${this.formatBytes(config.maxDiskUsage)}), starting cleanup...`);
      await this.runCleanup();
    }
  }

  /**
   * 清理单个会话的检查点
   */
  private async cleanupSession(
    sessionId: string,
    config: CheckpointConfig
  ): Promise<{
    sessionId: string;
    deleted: number;
    freedSpace: number;
  }> {
    const cleanupOptions = {
      keepRecent: config.autoCleanup.strategy.keepRecent,
      keepDays: config.autoCleanup.strategy.keepDays,
      keepByType: config.autoCleanup.strategy.keepByType,
      execute: true,
    };

    const result = await this.checkpointManager.cleanup(sessionId, cleanupOptions);

    return {
      sessionId,
      deleted: result.toDelete.length,
      freedSpace: result.freedSpace,
    };
  }

  /**
   * 获取所有会话ID
   * 这里需要根据实际的会话管理机制来实现
   */
  private async getAllSessionIds(): Promise<string[]> {
    // TODO: 实现获取所有活跃会话ID的逻辑
    // 可能需要从会话管理器、数据库或文件系统中获取
    return ["default-session"];
  }

  /**
   * 调度下一次清理
   */
  private scheduleNextCleanup(config: CheckpointConfig): void {
    if (!this.isRunning) {
      return;
    }

    const intervalMs = config.autoCleanup.intervalHours * 60 * 60 * 1000;
    
    this.cleanupTimer = setTimeout(async () => {
      try {
        await this.runCleanup();
        await this.checkDiskUsageAndCleanup();
      } catch (error) {
        console.error("Scheduled cleanup failed:", error);
      }

      // 调度下一次清理
      const updatedConfig = await this.configManager.load();
      this.scheduleNextCleanup(updatedConfig);
    }, intervalMs);
  }

  /**
   * 格式化字节数
   */
  private formatBytes(bytes: number): string {
    if (bytes === 0) return "0 Bytes";

    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB", "TB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  }

  /**
   * 获取清理预览（不执行实际清理）
   */
  async getCleanupPreview(sessionId?: string): Promise<{
    totalToDelete: number;
    estimatedFreedSpace: number;
    sessionPreviews: Array<{
      sessionId: string;
      toDelete: number;
      estimatedFreedSpace: number;
      checkpoints: Array<{
        id: string;
        timestamp: number;
        message: string;
        type: string;
      }>;
    }>;
  }> {
    const config = await this.configManager.load();
    const results = {
      totalToDelete: 0,
      estimatedFreedSpace: 0,
      sessionPreviews: [] as Array<{
        sessionId: string;
        toDelete: number;
        estimatedFreedSpace: number;
        checkpoints: Array<{
          id: string;
          timestamp: number;
          message: string;
          type: string;
        }>;
      }>,
    };

    const sessionIds = sessionId ? [sessionId] : await this.getAllSessionIds();

    for (const sid of sessionIds) {
      try {
        const cleanupOptions = {
          keepRecent: config.autoCleanup.strategy.keepRecent,
          keepDays: config.autoCleanup.strategy.keepDays,
          keepByType: config.autoCleanup.strategy.keepByType,
          execute: false, // 预览模式
        };

        const result = await this.checkpointManager.cleanup(sid, cleanupOptions);
        
        const preview = {
          sessionId: sid,
          toDelete: result.toDelete.length,
          estimatedFreedSpace: result.freedSpace,
          checkpoints: result.toDelete.map((cp: any) => ({
            id: cp.id,
            timestamp: cp.timestamp,
            message: cp.message,
            type: cp.type,
          })),
        };

        results.sessionPreviews.push(preview);
        results.totalToDelete += preview.toDelete;
        results.estimatedFreedSpace += preview.estimatedFreedSpace;
      } catch (error) {
        console.error(`Failed to get cleanup preview for session ${sid}:`, error);
      }
    }

    return results;
  }
}
