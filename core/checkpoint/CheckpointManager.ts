import { IDE } from "../index.js";
import { ShadowCheckpointService } from "./ShadowCheckpointService.js";
import { CheckpointType, CheckpointMetadata } from "./types.js";

/**
 * 检查点管理器
 * 负责管理多个会话的检查点服务，并提供统一的接口
 */
export class CheckpointManager {
  private services = new Map<string, ShadowCheckpointService>();
  private ide: IDE;
  private workspaceDir: string;

  constructor(ide: IDE) {
    this.ide = ide;
    this.workspaceDir = "";
  }

  /**
   * 初始化检查点管理器
   */
  async init(): Promise<void> {
    // 获取工作区目录
    const workspaceDirs = await this.ide.getWorkspaceDirs();
    this.workspaceDir = workspaceDirs[0] || "";
    
    if (!this.workspaceDir) {
      console.warn("No workspace directory found, checkpoint functionality disabled");
      return;
    }
  }

  /**
   * 获取或创建会话的检查点服务
   */
  async getOrCreateService(sessionId: string): Promise<ShadowCheckpointService> {
    if (!this.services.has(sessionId)) {
      const service = new ShadowCheckpointService({
        workspaceDir: this.workspaceDir,
        sessionId,
        author: {
          name: "AIMI Bot",
          email: "<EMAIL>",
        },
      });

      await service.init();
      await service.start();
      
      this.services.set(sessionId, service);
    }

    return this.services.get(sessionId)!;
  }

  /**
   * 创建检查点
   */
  async createCheckpoint(
    sessionId: string,
    message: string,
    type: CheckpointType = CheckpointType.MANUAL,
    options?: {
      conversationId?: string;
      messageId?: string;
      tags?: string[];
      taskId?: string;
      customFields?: Record<string, any>;
    }
  ): Promise<CheckpointMetadata> {
    const service = await this.getOrCreateService(sessionId);
    return service.createCheckpoint(message, type, options);
  }

  /**
   * 在对话完成时自动创建检查点
   */
  async onConversationEnd(
    sessionId: string,
    conversationId: string,
    messageId?: string
  ): Promise<CheckpointMetadata> {
    return this.createCheckpoint(
      sessionId,
      `Conversation completed: ${conversationId}`,
      CheckpointType.CONVERSATION_END,
      {
        conversationId,
        messageId,
        tags: ["auto", "conversation"],
      }
    );
  }

  /**
   * 在应用编辑后创建检查点
   */
  async onApplyEdits(
    sessionId: string,
    conversationId?: string,
    messageId?: string,
    editDescription?: string
  ): Promise<CheckpointMetadata> {
    const message = editDescription 
      ? `Applied edits: ${editDescription}`
      : "Applied edits";
      
    return this.createCheckpoint(
      sessionId,
      message,
      CheckpointType.APPLY_EDITS,
      {
        conversationId,
        messageId,
        tags: ["auto", "edits"],
      }
    );
  }

  /**
   * 在命令执行前创建检查点
   */
  async onBeforeCommand(
    sessionId: string,
    command: string,
    conversationId?: string
  ): Promise<CheckpointMetadata> {
    return this.createCheckpoint(
      sessionId,
      `Before command: ${command}`,
      CheckpointType.BEFORE_COMMAND,
      {
        conversationId,
        tags: ["auto", "command", "before"],
        customFields: { command },
      }
    );
  }

  /**
   * 在命令执行后创建检查点
   */
  async onAfterCommand(
    sessionId: string,
    command: string,
    success: boolean,
    conversationId?: string
  ): Promise<CheckpointMetadata> {
    const status = success ? "success" : "failed";
    return this.createCheckpoint(
      sessionId,
      `After command: ${command} (${status})`,
      CheckpointType.AFTER_COMMAND,
      {
        conversationId,
        tags: ["auto", "command", "after", status],
        customFields: { command, success },
      }
    );
  }

  /**
   * 获取会话的检查点列表
   */
  async listCheckpoints(sessionId: string, options?: any): Promise<CheckpointMetadata[]> {
    const service = this.services.get(sessionId);
    if (!service) {
      return [];
    }
    return service.listCheckpoints(options);
  }

  /**
   * 获取检查点详情
   */
  async getCheckpoint(sessionId: string, checkpointId: string): Promise<CheckpointMetadata | null> {
    const service = this.services.get(sessionId);
    if (!service) {
      return null;
    }
    return service.getCheckpoint(checkpointId);
  }

  /**
   * 恢复检查点
   */
  async restoreCheckpoint(
    sessionId: string,
    checkpointId: string,
    options?: any
  ): Promise<void> {
    const service = this.services.get(sessionId);
    if (!service) {
      throw new Error(`No checkpoint service found for session ${sessionId}`);
    }
    return service.restore(checkpointId, options);
  }

  /**
   * 删除检查点
   */
  async deleteCheckpoint(sessionId: string, checkpointId: string): Promise<void> {
    const service = this.services.get(sessionId);
    if (!service) {
      throw new Error(`No checkpoint service found for session ${sessionId}`);
    }
    return service.deleteCheckpoint(checkpointId);
  }

  /**
   * 获取检查点差异
   */
  async getCheckpointDiff(
    sessionId: string,
    checkpointId: string,
    compareWith?: string
  ): Promise<any[]> {
    const service = this.services.get(sessionId);
    if (!service) {
      throw new Error(`No checkpoint service found for session ${sessionId}`);
    }
    return service.getDiff(checkpointId, compareWith);
  }

  /**
   * 获取检查点统计信息
   */
  async getStats(sessionId: string): Promise<any> {
    const service = this.services.get(sessionId);
    if (!service) {
      return {
        totalCount: 0,
        diskUsage: 0,
        byType: {},
      };
    }
    return service.getStats();
  }

  /**
   * 清理检查点
   */
  async cleanup(sessionId: string, options?: any): Promise<any> {
    const service = this.services.get(sessionId);
    if (!service) {
      return {
        toDelete: [],
        freedSpace: 0,
        executed: false,
      };
    }
    return service.cleanup(options);
  }

  /**
   * 停止会话的检查点服务
   */
  async stopSession(sessionId: string): Promise<void> {
    const service = this.services.get(sessionId);
    if (service) {
      await service.stop();
      this.services.delete(sessionId);
    }
  }

  /**
   * 停止所有检查点服务
   */
  async stopAll(): Promise<void> {
    for (const [sessionId, service] of Array.from(this.services.entries())) {
      await service.stop();
    }
    this.services.clear();
  }
}
