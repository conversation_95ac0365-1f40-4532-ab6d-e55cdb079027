import { Tool } from "../..";
import { BUILT_IN_GROUP_NAME, BuiltInToolNames } from "../builtIn";

export interface TextEditorArgs {
  command:
    | "view"
    | "create"
    | "str_replace"
    | "insert"
    | "undo_edit"
    | "delete";
  path: string;
  file_text?: string;
  view_range?: [number, number];
  old_str?: string;
  new_str?: string;
  insert_line?: number;
}

const SEARCH_REPLACE_EDITOR_DESCRIPTION = `Custom editing tool for viewing, creating and editing files
* State is persistent across command calls and discussions with the user
* If \`path\` is a file, \`view\` displays the result of applying \`cat -n\`. If \`path\` is a directory, \`view\` lists non-hidden files and directories up to 2 levels deep
* The \`create\` command cannot be used if the specified \`path\` already exists as a file
* If a \`command\` generates a long output, it will be truncated and marked with \`<response clipped>\`
* The \`undo_edit\` command will revert the last edit made to the file at \`path\`
* The \`delete\` command will permanently delete the file at \`path\`

Notes for using the \`view\` command:
* If \`path\` is a file, \`view\` shows the file content
* If \`path\` is a directory, \`view\` shows a directory tree up to 2 levels deep
* The \`view_range\` parameter can be used to pass a line range of the file to be shown, e.g. \`[10, 20]\`
* the \`path\` parameter can be absolute path or uri to (file/directory) or relative path to file in workspace
* Only the \`view\` command supports the \`path\` being an absolute file path or uri. For other commands, the \`path\` must be relative to the workspace root.

Notes for using the \`str_replace\` command:
* The \`old_str\` parameter should match EXACTLY one or more consecutive lines from the original file. Be mindful of whitespaces!
* If the \`old_str\` parameter is not unique in the file, the replacement will not be performed. Make sure to include enough context in \`old_str\` to make it unique
* The \`new_str\` parameter should contain the edited lines that should replace the \`old_str\`
* Do not apply formatting or beautification changes unless explicitly requested by the user
* Preserve the existing code style, indentation, and formatting patterns in the file`;

export const textEditorTool: Tool = {
  type: "function",
  displayTitle: "文件编辑器",
  wouldLikeTo: "edit {{{ path }}}",
  isCurrently: "editing {{{ path }}}",
  hasAlready: "edited {{{ path }}}",
  group: BUILT_IN_GROUP_NAME,
  readonly: false,
  isInstant: false,
  function: {
    name: BuiltInToolNames.SearchReplaceEditor,
    description: SEARCH_REPLACE_EDITOR_DESCRIPTION,
    parameters: {
      type: "object",
      required: ["command", "path"],
      properties: {
        command: {
          type: "string",
          description:
            "The commands to run. Allowed options are: `view`, `create`, `str_replace`, `insert`, `undo_edit`, `delete`.",
          enum: [
            "view",
            "create",
            "str_replace",
            "insert",
            "undo_edit",
            "delete",
          ],
        },
        path: {
          type: "string",
          description:
            "relative path to file or directory in the workspace, e.g. `/relative_path/file.py` or `/relative_path`.",
        },
        file_text: {
          type: "string",
          description:
            "Required parameter of `create` command, with the content of the file to be created.",
        },
        old_str: {
          type: "string",
          description:
            "Required parameter of `str_replace` command containing the string in `path` to replace.",
        },
        new_str: {
          type: "string",
          description:
            "Optional parameter of `str_replace` command containing the new string (if not given, no string will be added). Required parameter of `insert` command containing the string to insert.",
        },
        insert_line: {
          type: "integer",
          description:
            "Required parameter of `insert` command. The `new_str` will be inserted AFTER the line `insert_line` of `path`.",
        },
        view_range: {
          type: "array",
          items: {
            type: "integer",
          },
          description:
            "Optional parameter of `view` command when `path` points to a file. If none is given, the full file is shown. If provided, the file will be shown in the indicated line number range, e.g. [11, 12] will show lines 11 and 12. Indexing at 1 to start. Setting `[start_line, -1]` shows all lines from `start_line` to the end of the file.",
        },
      },
    },
  },
  systemMessageDescription: {
    prefix: `To view, create, or edit files, use the ${BuiltInToolNames.SearchReplaceEditor} tool.

Examples:
- View a file: {"command": "view", "path": "/relative_path/file.py"}
- Create a file: {"command": "create", "path": "/relative_path/new_file.py", "file_text": "print('hello')"}
- Replace text: {"command": "str_replace", "path": "/relative_path/file.py", "old_str": "old code", "new_str": "new code"}
- Insert text: {"command": "insert", "path": "/relative_path/file.py", "insert_line": 10, "new_str": "new line"}
- Undo last edit: {"command": "undo_edit", "path": "/relative_path/file.py"}
- Delete a file: {"command": "delete", "path": "/relative_path/file.py"}`,
    exampleArgs: [
      ["command", "str_replace"],
      ["path", "/relative_path/example.py"],
      ["old_str", "def old_function():\n    pass"],
      ["new_str", "def new_function():\n    return True"],
    ],
  },
};
