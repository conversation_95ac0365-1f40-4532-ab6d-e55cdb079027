package com.taobao.mc.aimi.ext.toolWindow

import com.google.gson.Gson
import com.google.gson.JsonObject
import com.google.gson.JsonParser
import com.intellij.openapi.components.service
import com.intellij.openapi.project.Project
import com.intellij.openapi.util.Disposer
import com.intellij.ui.jcef.JBCefBrowser
import com.intellij.ui.jcef.JBCefBrowserBase
import com.intellij.ui.jcef.JBCefClient.Properties
import com.intellij.ui.jcef.JBCefJSQuery
import com.intellij.ui.jcef.executeJavaScriptAsync
import com.intellij.util.application
import com.taobao.mc.aimi.ext.activities.AIMIPluginDisposable
import com.taobao.mc.aimi.ext.factories.CustomSchemeHandlerFactory
import com.taobao.mc.aimi.ext.services.AIMIPluginService
import com.taobao.mc.aimi.ext.utils.uuid
import com.taobao.mc.aimi.logger.LoggerManager
import com.taobao.mc.aimi.settings.AIMISettingService
import com.taobao.mc.aimi.types.MessageTypes
import kotlinx.coroutines.CompletableDeferred
import kotlinx.coroutines.launch
import org.cef.CefApp
import org.cef.browser.CefBrowser
import org.cef.browser.CefFrame
import org.cef.handler.CefLoadHandlerAdapter
import org.cef.network.CefRequest

class AIMIBrowser(val project: Project, var url: String) {
    private val logger = LoggerManager.getLogger(AIMIBrowser::class.java)

    val browser: JBCefBrowser

    private val continuePluginService: AIMIPluginService = project.getService(AIMIPluginService::class.java)

    val isWebviewReady = CompletableDeferred(false)

    init {
        val isOSREnabled = application.getService(AIMISettingService::class.java).state.enableOSR

        this.browser = JBCefBrowser.createBuilder()
            .setOffScreenRendering(isOSREnabled)
            .setEnableOpenDevToolsMenuItem(true)
            .build()
            .apply {
                // To avoid using System.setProperty to affect other plugins,
                // we should configure JS_QUERY_POOL_SIZE after JBCefClient is instantiated,
                // and eliminate the 'Uncaught TypeError: window.cefQuery_xxx is not a function' error
                // in the JS debug console, which is caused by AIMIBrowser lazy loading.
                jbCefClient.setProperty(Properties.JS_QUERY_POOL_SIZE, JS_QUERY_POOL_SIZE)
                jbCefClient.setProperty("ide.browser.jcef.contextMenu.devTools.enabled", "true")
            }

        registerAppSchemeHandler()
        Disposer.register(project.service<AIMIPluginDisposable>(), browser)

        // Listen for events sent from browser
        val myJSQueryOpenInBrowser = JBCefJSQuery.create((browser as JBCefBrowserBase?)!!)

        myJSQueryOpenInBrowser.addHandler { msg: String? ->
            val json: JsonObject = JsonParser.parseString(msg ?: "").asJsonObject
            val messageType = json.get("messageType").asString
            val data = json.get("data")
            val messageId = json.get("messageId")?.asString

            val iMessageType = MessageTypes.from(messageType)

            val respond = fun(data: Any?) {
                sendToWebview(messageType, data, messageId ?: uuid())
            }

            if (iMessageType is MessageTypes.ToCore) {
                continuePluginService.coreMessenger?.request(iMessageType, data, messageId, respond)
                return@addHandler null
            }

            // If not pass through, then put it in the status/content/done format for webview
            // Core already sends this format
            val respondToWebview = fun(data: Any?) {
                sendToWebview(
                    messageType, mapOf(
                        "status" to "success",
                        "content" to data,
                        "done" to true
                    ), messageId ?: uuid()
                )
            }

            if (msg != null && MessageTypes.ideMessageTypes.contains(iMessageType)) {
                continuePluginService.ideProtocolClient?.handleMessage(msg, respondToWebview)
            }

            if (MessageTypes.webviewMessageTypes.contains(iMessageType)) {
                logger.debug("来自webview的 ToWebview 消息: ${iMessageType}, 不做任何处理")
                return@addHandler null
            }

            null
        }

        // Listen for the page load event
        browser.jbCefClient.addLoadHandler(object : CefLoadHandlerAdapter() {
            // 页面加载开始的时候, 注入 jsbridge, 防止登录拿不到信息
            override fun onLoadStart(browser: CefBrowser?, frame: CefFrame?, transitionType: CefRequest.TransitionType?) {
                isWebviewReady.complete(true)
                executeJavaScript(browser, myJSQueryOpenInBrowser)
            }
        }, browser.cefBrowser)

        // Load the url only after the protocolClient is initialized,
        // otherwise some messages will be lost, which are some configurations when the page is loaded.
        // Moreover, we should add LoadHandler before loading the url.
        continuePluginService.coroutineScope.launch {
            continuePluginService.awaitIdeProtocolClient()
            browser.loadURL(url)
        }
    }

    fun executeJavaScript(browser: CefBrowser?, myJSQueryOpenInBrowser: JBCefJSQuery) {
        // Execute JavaScript - you might want to handle potential exceptions here
        val script = """window.postIntellijMessage = function(messageType, data, messageId) {
                const msg = JSON.stringify({messageType, data, messageId});
                ${myJSQueryOpenInBrowser.inject("msg")}
            };
            localStorage.setItem("ide", '"jetbrains"');
            """.trimIndent()

        browser?.executeJavaScript(script, browser.url, 0)
    }

    @Suppress("DEPRECATION")
    fun sendToWebview(
        messageType: String,
        data: Any?,
        messageId: String = uuid()
    ) {
        val jsonData = Gson().toJson(
            mapOf(
                "messageId" to messageId,
                "messageType" to messageType,
                "data" to data
            )
        )
        val jsCode = buildJavaScript(jsonData)

        if (browser.isDisposed) {
            logger.warn("sendToWebView called because browser already disposed. jsonData: $jsonData")
            return
        }
        try {
            this.browser.executeJavaScriptAsync(jsCode).onError {
                logger.warn("Failed to execute jsCode error: ${it.message}")
            }
        } catch (error: IllegalStateException) {
            logger.warn("Webview not initialized yet $error")
        }
    }

    private fun registerAppSchemeHandler() {
        CefApp.getInstance().registerSchemeHandlerFactory(
            "http",
            "core",
            CustomSchemeHandlerFactory()
        )
    }

    private fun buildJavaScript(jsonData: String): String {
        return """window.postMessage($jsonData, "*");"""
    }

}
